import { ollamaChat } from "../utils/ollamaClient.js";

export const basicChat = async (req, res) => {
  try {
    const { message } = req.body;
    console.log("Received message:", message);

    const response = await ollamaChat(message);
    res.json({ reply: response });
  } catch (error) {
    console.error("Error details:", error);

    // Check if it's a connection error to Ollama
    if (error.code === 'ECONNREFUSED' || error.message.includes('ECONNREFUSED')) {
      res.status(503).json({
        error: "Ollama service is not running. Please start Ollama first.",
        details: "Run 'ollama serve' to start the Ollama service"
      });
    } else {
      res.status(500).json({
        error: "Something went wrong",
        details: error.message
      });
    }
  }
};
