import fetch from "node-fetch";

export const ollamaChat = async (prompt) => {
  const response = await fetch("http://localhost:11434/api/generate", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      model: "llama3",
      prompt: prompt,
    }),
  });

  if (!response.ok) {
    throw new Error(`HTTP error! status: ${response.status}`);
  }

  // Get the response text directly
  const result = await response.text();

  // Ollama streams responses, so extract text cleanly
  const lastLine = result.trim().split("\n").pop();
  return JSON.parse(lastLine).response;
};
