import fetch from "node-fetch";

export const ollamaChat = async (prompt) => {
  const response = await fetch("http://localhost:11434/api/generate", {
    method: "POST",
    headers: { "Content-Type": "application/json" },
    body: JSON.stringify({
      model: "llama3",
      prompt: prompt,
    }),
  });

  const reader = response.body.getReader();
  let result = "";

  while (true) {
    const { done, value } = await reader.read();
    if (done) break;
    result += new TextDecoder().decode(value);
  }

  // Ollama streams responses, so extract text cleanly
  const lastLine = result.trim().split("\n").pop();
  return JSON.parse(lastLine).response;
};
