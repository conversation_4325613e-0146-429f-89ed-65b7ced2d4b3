import express from "express";
import { basicChat } from "../controllers/aiController.js";

const router = express.Router();

// Test AI route
router.post("/", basicChat);

// Test endpoint that doesn't require Ollama
router.post("/test", (req, res) => {
  const { message } = req.body;
  res.json({
    reply: `Echo: ${message}`,
    status: "API is working correctly",
    timestamp: new Date().toISOString()
  });
});

export default router;
